#!/bin/bash

# Простой тест SSL менеджера

echo "🧪 Тестируем SSL менеджер..."

# Проверяем что файл переменных окружения существует
if [ ! -f "/etc/edu_telebot/env" ]; then
    echo "❌ Файл /etc/edu_telebot/env не найден"
    echo "💡 Создайте его командой: sudo ./scripts/setup_env.sh"
    exit 1
fi

# Загружаем переменные
source /etc/edu_telebot/env

echo "✅ Переменные окружения загружены"
echo "📍 Домен: $DOMAIN"

# Проверяем синтаксис скрипта
echo "🔍 Проверяем синтаксис SSL менеджера..."
if bash -n scripts/ssl_manager.sh; then
    echo "✅ Синтаксис корректен"
else
    echo "❌ Ошибка синтаксиса в SSL менеджере"
    exit 1
fi

# Запускаем проверку SSL статуса
echo "🔍 Проверяем текущий статус SSL..."
./scripts/ssl_manager.sh --check

echo ""
echo "🎉 Тест завершен!"
echo "💡 Для полной настройки SSL запустите: ./scripts/ssl_manager.sh"
