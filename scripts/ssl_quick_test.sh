#!/bin/bash

# Быстрый тест SSL настройки

echo "🧪 Быстрый тест SSL настройки"
echo "=============================="

# Проверяем переменные окружения
if [ -f "/etc/edu_telebot/env" ]; then
    source /etc/edu_telebot/env
    echo "✅ Переменные окружения загружены"
    echo "📍 Домен: $DOMAIN"
else
    echo "❌ Файл /etc/edu_telebot/env не найден"
    echo "💡 Создайте его: sudo ./scripts/setup_env.sh"
    exit 1
fi

# Проверяем домен
if [ -z "$DOMAIN" ] || [ "$DOMAIN" = "your-domain.com" ]; then
    echo "❌ Домен не настроен"
    echo "💡 Отредактируйте /etc/edu_telebot/env и установите правильный DOMAIN"
    exit 1
fi

# Проверяем DNS
echo "🔍 Проверяем DNS записи..."
SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || echo "unknown")
DOMAIN_IP=$(nslookup $DOMAIN 2>/dev/null | grep "Address:" | tail -1 | awk '{print $2}' || echo "unknown")

echo "📍 IP сервера: $SERVER_IP"
echo "📍 IP домена: $DOMAIN_IP"

if [ "$SERVER_IP" = "$DOMAIN_IP" ]; then
    echo "✅ DNS записи настроены правильно"
else
    echo "⚠️ DNS записи не совпадают"
    echo "💡 Убедитесь что A запись $DOMAIN указывает на $SERVER_IP"
fi

# Проверяем порт 80
echo "🔍 Проверяем доступность порта 80..."
if netstat -tuln 2>/dev/null | grep -q ":80 "; then
    echo "⚠️ Порт 80 занят"
    netstat -tuln | grep ":80 "
else
    echo "✅ Порт 80 свободен"
fi

# Проверяем acme.sh
if [ -d "$HOME/.acme.sh" ]; then
    echo "✅ acme.sh установлен"
    echo "📋 Версия: $($HOME/.acme.sh/acme.sh --version)"
else
    echo "⚠️ acme.sh не установлен"
fi

# Проверяем существующие сертификаты
if [ -f "nginx/ssl/fullchain.pem" ] && [ -f "nginx/ssl/privkey.pem" ]; then
    echo "✅ SSL сертификаты найдены"
    if command -v openssl &> /dev/null; then
        expiry=$(openssl x509 -enddate -noout -in nginx/ssl/fullchain.pem 2>/dev/null | cut -d= -f2)
        if [ -n "$expiry" ]; then
            echo "📅 Срок действия: $expiry"
        fi
    fi
else
    echo "❌ SSL сертификаты не найдены"
fi

echo ""
echo "🎯 Рекомендации:"
if [ "$SERVER_IP" != "$DOMAIN_IP" ]; then
    echo "   1. Настройте DNS записи"
fi
if netstat -tuln 2>/dev/null | grep -q ":80 "; then
    echo "   2. Освободите порт 80 для получения SSL"
fi
if [ ! -d "$HOME/.acme.sh" ]; then
    echo "   3. Установите acme.sh"
fi
echo "   4. Запустите полную настройку: ./scripts/ssl_manager.sh"

echo ""
echo "✅ Тест завершен!"
