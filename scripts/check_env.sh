#!/bin/bash

# Проверка переменных окружения

echo "🔍 Проверка переменных окружения"
echo "================================"

# Проверяем файл
if [ ! -f "/etc/edu_telebot/env" ]; then
    echo "❌ Файл /etc/edu_telebot/env не найден"
    echo "💡 Создайте его: sudo ./scripts/setup_env.sh"
    exit 1
fi

echo "✅ Файл /etc/edu_telebot/env найден"

# Показываем содержимое файла
echo ""
echo "📋 Содержимое файла:"
echo "===================="
cat /etc/edu_telebot/env

# Загружаем переменные
echo ""
echo "🔄 Загружаем переменные..."
source /etc/edu_telebot/env

# Проверяем каждую переменную
echo ""
echo "📊 Проверка переменных:"
echo "======================"

echo "BOT_TOKEN: ${BOT_TOKEN:0:10}... (${#BOT_TOKEN} символов)"
echo "POSTGRES_DB: $POSTGRES_DB"
echo "POSTGRES_USER: $POSTGRES_USER"
echo "POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:0:3}... (${#POSTGRES_PASSWORD} символов)"
echo "ENVIRONMENT: $ENVIRONMENT"
echo "DOMAIN: $DOMAIN"
echo "WEBHOOK_MODE: $WEBHOOK_MODE"
echo "WEBHOOK_HOST: $WEBHOOK_HOST"
echo "WEBHOOK_PATH: $WEBHOOK_PATH"
echo "WEB_SERVER_HOST: $WEB_SERVER_HOST"
echo "WEB_SERVER_PORT: $WEB_SERVER_PORT"

# Проверяем критичные переменные
echo ""
echo "⚠️ Проверка критичных переменных:"
echo "================================"

if [ -z "$BOT_TOKEN" ] || [ "$BOT_TOKEN" = "your_bot_token_here" ]; then
    echo "❌ BOT_TOKEN не настроен"
else
    echo "✅ BOT_TOKEN настроен"
fi

if [ -z "$DOMAIN" ] || [ "$DOMAIN" = "your-domain.com" ]; then
    echo "❌ DOMAIN не настроен"
else
    echo "✅ DOMAIN настроен: $DOMAIN"
fi

if [ -z "$POSTGRES_PASSWORD" ] || [ "$POSTGRES_PASSWORD" = "your_secure_password_here" ]; then
    echo "❌ POSTGRES_PASSWORD не настроен"
else
    echo "✅ POSTGRES_PASSWORD настроен"
fi

echo ""
echo "🎯 Рекомендации:"
if [ -z "$BOT_TOKEN" ] || [ "$BOT_TOKEN" = "your_bot_token_here" ]; then
    echo "   1. Получите токен бота от @BotFather"
    echo "   2. Установите BOT_TOKEN в /etc/edu_telebot/env"
fi

if [ -z "$DOMAIN" ] || [ "$DOMAIN" = "your-domain.com" ]; then
    echo "   3. Настройте домен в DOMAIN (например: edubot.schoolpro.kz)"
fi

if [ -z "$POSTGRES_PASSWORD" ] || [ "$POSTGRES_PASSWORD" = "your_secure_password_here" ]; then
    echo "   4. Установите надежный пароль для POSTGRES_PASSWORD"
fi

echo ""
echo "✅ Проверка завершена!"
