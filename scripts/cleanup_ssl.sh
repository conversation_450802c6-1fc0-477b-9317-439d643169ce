#!/bin/bash

# Скрипт полной очистки SSL настроек

echo "🧹 Полная очистка SSL настроек"
echo "=============================="

read -p "⚠️ Это удалит все SSL сертификаты и настройки acme.sh. Продолжить? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Отменено пользователем"
    exit 0
fi

echo "🗑️ Начинаем очистку..."

# 1. Останавливаем процессы acme.sh
echo "🛑 Останавливаем процессы acme.sh..."
pkill -f acme.sh 2>/dev/null || true

# 2. Удаляем acme.sh встроенной командой
if [ -d "$HOME/.acme.sh" ] && [ -f "$HOME/.acme.sh/acme.sh" ]; then
    echo "🗑️ Удаляем acme.sh встроенной командой..."
    $HOME/.acme.sh/acme.sh --uninstall 2>/dev/null || true
fi

# 3. Принудительно удаляем директорию
echo "🗑️ Удаляем директорию acme.sh..."
rm -rf $HOME/.acme.sh

# 4. Очищаем crontab
echo "🗑️ Очищаем crontab..."
if crontab -l 2>/dev/null | grep -q acme; then
    echo "   Найдены записи acme.sh в crontab, удаляем..."
    crontab -l 2>/dev/null | grep -v acme.sh | crontab - 2>/dev/null || true
fi

# 5. Удаляем системные cron задачи
echo "🗑️ Удаляем системные cron задачи..."
sudo rm -f /etc/cron.daily/ssl-renewal
sudo rm -f /etc/cron.d/*acme* 2>/dev/null || true
sudo rm -f /etc/cron.weekly/*acme* 2>/dev/null || true
sudo rm -f /etc/cron.monthly/*acme* 2>/dev/null || true

# 6. Очищаем из shell профилей
echo "🗑️ Очищаем из shell профилей..."
for file in $HOME/.bashrc $HOME/.profile $HOME/.zshrc; do
    if [ -f "$file" ]; then
        if grep -q acme.sh "$file"; then
            echo "   Очищаем $file..."
            sed -i '/acme\.sh/d' "$file"
        fi
    fi
done

# 7. Удаляем SSL сертификаты проекта
echo "🗑️ Удаляем SSL сертификаты проекта..."
if [ -d "nginx/ssl" ]; then
    echo "   Найдены SSL сертификаты в nginx/ssl/"
    read -p "   Удалить SSL сертификаты? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -f nginx/ssl/*.pem
        rm -f nginx/ssl/*.crt
        rm -f nginx/ssl/*.key
        echo "   ✅ SSL сертификаты удалены"
    else
        echo "   ⏭️ SSL сертификаты сохранены"
    fi
fi

# 8. Удаляем логи SSL
echo "🗑️ Удаляем логи SSL..."
rm -rf logs/ssl/ 2>/dev/null || true

# 9. Удаляем временные файлы
echo "🗑️ Удаляем временные файлы..."
rm -rf /tmp/*acme* 2>/dev/null || true
rm -rf /tmp/tmp.*acme* 2>/dev/null || true

# 10. Очищаем переменные окружения
echo "🗑️ Очищаем переменные окружения..."
unset ACME_SH_PATH 2>/dev/null || true

# 11. Проверяем результат
echo ""
echo "🔍 Проверяем результат очистки..."

if [ -d "$HOME/.acme.sh" ]; then
    echo "⚠️ Директория $HOME/.acme.sh все еще существует"
else
    echo "✅ Директория acme.sh удалена"
fi

if command -v acme.sh &> /dev/null; then
    echo "⚠️ Команда acme.sh все еще доступна"
else
    echo "✅ Команда acme.sh недоступна"
fi

if crontab -l 2>/dev/null | grep -q acme; then
    echo "⚠️ Найдены записи acme в crontab:"
    crontab -l | grep acme
else
    echo "✅ Crontab очищен от записей acme"
fi

echo ""
echo "🎉 Очистка завершена!"
echo ""
echo "💡 Что дальше:"
echo "   1. Для новой установки SSL: ./scripts/ssl_manager.sh"
echo "   2. Для проверки переменных: ./scripts/check_env.sh"
echo "   3. Для деплоя без SSL: отключите WEBHOOK_MODE в /etc/edu_telebot/env"

echo ""
echo "⚠️ Примечание: Перезайдите в терминал для применения изменений в PATH"
